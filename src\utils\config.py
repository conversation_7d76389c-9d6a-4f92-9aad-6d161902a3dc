"""
配置管理模块
负责管理系统的各种配置参数，包括并发控制、目录结构等
"""


from dataclasses import dataclass
from typing import Optional
from pathlib import Path
import json
import logging

logger = logging.getLogger(__name__)





@dataclass
class DirectoryConfig:
    """目录结构配置"""
    base_receive_dir: str = "tmp_file_receive"  # 文件接收目录
    base_markdown_dir: str = "tmp_file_markdown"  # Markdown输出目录
    base_document_tree_dir: str = "tmp_document_tree"  # 文档树目录
    
    # 目录结构：document_id/snow_id/文档
    use_snow_id_subdirs: bool = True  # 是否使用snow_id作为子目录


@dataclass
class ProcessingConfig:
    """处理配置"""
    # 文件存在性检查
    check_existing_files: bool = True  # 是否检查文件是否已存在
    skip_existing_files: bool = True   # 是否跳过已存在的文件
    
    # 文件类型支持
    supported_pdf_extensions: list = None
    supported_html_extensions: list = None
    supported_markdown_extensions: list = None
    
    def __post_init__(self):
        if self.supported_pdf_extensions is None:
            self.supported_pdf_extensions = ['.pdf']
        if self.supported_html_extensions is None:
            self.supported_html_extensions = ['.html', '.htm']
        if self.supported_markdown_extensions is None:
            self.supported_markdown_extensions = ['.md', '.markdown']


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径，如果为None则使用默认配置
        """
        self.config_file = config_file or "config.json"
        self.directory = DirectoryConfig()
        self.processing = ProcessingConfig()
        
        # 加载配置
        self._load_config()
        
        # 确保目录存在
        self._ensure_directories()
    
    def _load_config(self):
        """加载配置文件"""
        config_path = Path(self.config_file)
        
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 更新目录配置
                if 'directory' in config_data:
                    directory_data = config_data['directory']
                    for key, value in directory_data.items():
                        if hasattr(self.directory, key):
                            setattr(self.directory, key, value)
                
                # 更新处理配置
                if 'processing' in config_data:
                    processing_data = config_data['processing']
                    for key, value in processing_data.items():
                        if hasattr(self.processing, key):
                            setattr(self.processing, key, value)
                
                logger.info(f"配置文件加载成功: {config_path}")
                
            except Exception as e:
                logger.warning(f"配置文件加载失败，使用默认配置: {e}")
        else:
            logger.info("配置文件不存在，使用默认配置")
            # 创建默认配置文件
            self.save_config()
    
    def save_config(self):
        """保存配置到文件"""
        config_data = {
            'directory': {
                'base_receive_dir': self.directory.base_receive_dir,
                'base_markdown_dir': self.directory.base_markdown_dir,
                'base_document_tree_dir': self.directory.base_document_tree_dir,
                'use_snow_id_subdirs': self.directory.use_snow_id_subdirs,
            },
            'processing': {
                'check_existing_files': self.processing.check_existing_files,
                'skip_existing_files': self.processing.skip_existing_files,
                'supported_pdf_extensions': self.processing.supported_pdf_extensions,
                'supported_html_extensions': self.processing.supported_html_extensions,
                'supported_markdown_extensions': self.processing.supported_markdown_extensions,
            }
        }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            logger.info(f"配置文件保存成功: {self.config_file}")
        except Exception as e:
            logger.error(f"配置文件保存失败: {e}")
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            self.directory.base_receive_dir,
            self.directory.base_markdown_dir,
            self.directory.base_document_tree_dir
        ]
        
        for dir_path in directories:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    def get_file_path(self, document_id: str, snow_id: str, filename: str, 
                     dir_type: str = "receive") -> Path:
        """
        获取文件路径
        
        Args:
            document_id: 文档ID（知识库ID）
            snow_id: 雪花ID
            filename: 文件名
            dir_type: 目录类型 ("receive", "markdown", "tree")
        
        Returns:
            完整的文件路径
        """
        if dir_type == "receive":
            base_dir = Path(self.directory.base_receive_dir)
        elif dir_type == "markdown":
            base_dir = Path(self.directory.base_markdown_dir)
        elif dir_type == "tree":
            base_dir = Path(self.directory.base_document_tree_dir)
        else:
            raise ValueError(f"不支持的目录类型: {dir_type}")
        
        if self.directory.use_snow_id_subdirs:
            # 使用层级结构：document_id/snow_id/filename
            return base_dir / document_id / snow_id / filename
        else:
            # 使用扁平结构：document_id/filename
            return base_dir / document_id / filename
    



# 全局配置实例
config_manager = ConfigManager()

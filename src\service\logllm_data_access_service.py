"""
日志LLM数据接入服务
负责整体核心工作的流程调度和RESTful接口
"""

import time
import asyncio
from typing import Dict, Any, Optional
from fastapi import FastAPI, UploadFile, File, Form, HTTPException, BackgroundTasks
from pydantic import BaseModel
import logging
from pathlib import Path

from src.utils.data_process import DataProcess
from src.utils.config import config_manager
from src.module.file_reformat import FileReformat
from src.module.markdown_to_document_tree import MarkdownToDocumentTree


# 配置结构化日志（同时输出到控制台和文件）
import logging.handlers
from pathlib import Path

# 创建日志目录
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)

# 创建logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 创建formatter
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 控制台处理器
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(formatter)

# 文件处理器（按日期轮转）
file_handler = logging.handlers.TimedRotatingFileHandler(
    log_dir / "logllm_service.log",
    when='midnight',
    interval=1,
    backupCount=30,
    encoding='utf-8'
)
file_handler.setLevel(logging.INFO)
file_handler.setFormatter(formatter)

# 添加处理器
logger.addHandler(console_handler)
logger.addHandler(file_handler)

# 防止重复日志
logger.propagate = False


class FileUploadRequest(BaseModel):
    """文件上传请求模型（JSON方式）"""
    file_url: str
    document_id: str


class ProcessResult(BaseModel):
    """处理结果模型"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class LogLLMDataAccessService:
    """日志LLM数据接入服务"""
    
    def __init__(self):
        self.data_processor = DataProcess()
        self.file_reformatter = FileReformat()
        self.markdown_processor = MarkdownToDocumentTree()

        # 防止重复处理的锁
        self.processing_locks = set()

        # 创建FastAPI应用
        self.app = FastAPI(
            title="日志LLM数据接入服务",
            description="支持PDF、HTML、Markdown文件解析和文档树生成",
            version="1.0.0"
        )

        # 注册路由
        self._register_routes()
    
    def _register_routes(self):
        """注册API路由"""
        
        @self.app.post("/upload/url", response_model=ProcessResult)
        async def upload_file_by_url(request: FileUploadRequest, background_tasks: BackgroundTasks):
            """通过URL上传文件"""
            try:
                logger.info(f"开始处理URL文件上传: {request.file_url}, document_id: {request.document_id}")

                # 检查文件是否已存在
                filename = Path(request.file_url).name
                if config_manager.processing.skip_existing_files:
                    exists, existing_path = self.data_processor.check_file_exists(request.document_id, filename)
                    if exists:
                        logger.info(f"[UPLOAD_SKIP] 文件已存在，跳过处理 - document_id: {request.document_id}, file: {filename}, path: {existing_path}")
                        return ProcessResult(
                            success=True,
                            message="文件已存在，跳过处理",
                            data={"document_id": request.document_id, "status": "skipped", "existing_path": str(existing_path)}
                        )

                # 添加后台任务处理文件
                background_tasks.add_task(
                    self._process_file_pipeline,
                    "url",
                    request.document_id,
                    file_url=request.file_url
                )

                return ProcessResult(
                    success=True,
                    message="文件上传任务已启动，正在后台处理",
                    data={"document_id": request.document_id, "status": "processing"}
                )

            except Exception as e:
                logger.error(f"URL文件上传失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/upload/local", response_model=ProcessResult)
        async def upload_file_by_local_path(request: FileUploadRequest, background_tasks: BackgroundTasks):
            """通过本地路径上传文件"""
            try:
                logger.info(f"开始处理本地文件上传: {request.file_url}, document_id: {request.document_id}")

                # 验证本地文件是否存在
                file_path = Path(request.file_url)
                if not file_path.exists():
                    logger.error(f"[LOCAL_FILE_ERROR] 本地文件不存在: {request.file_url}")
                    raise HTTPException(status_code=404, detail=f"本地文件不存在: {request.file_url}")

                # 记录文件信息
                file_size = file_path.stat().st_size
                logger.info(f"[LOCAL_FILE_INFO] 文件信息 - path: {file_path}, size: {file_size} bytes")

                # 检查文件是否已存在
                filename = file_path.name
                if config_manager.processing.skip_existing_files:
                    exists, existing_path = self.data_processor.check_file_exists(request.document_id, filename)
                    if exists:
                        logger.info(f"[UPLOAD_SKIP] 文件已存在，跳过处理 - document_id: {request.document_id}, file: {filename}, path: {existing_path}")
                        return ProcessResult(
                            success=True,
                            message="文件已存在，跳过处理",
                            data={"document_id": request.document_id, "status": "skipped", "existing_path": str(existing_path)}
                        )

                # 添加后台任务处理文件
                background_tasks.add_task(
                    self._process_file_pipeline,
                    "local",
                    request.document_id,
                    file_path=request.file_url
                )

                return ProcessResult(
                    success=True,
                    message="文件上传任务已启动，正在后台处理",
                    data={"document_id": request.document_id, "status": "processing"}
                )

            except HTTPException:
                raise  # 重新抛出HTTP异常，保持原始状态码
            except Exception as e:
                logger.error(f"本地文件上传失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/upload/formdata", response_model=ProcessResult)
        async def upload_file_by_formdata(
            background_tasks: BackgroundTasks,
            file: UploadFile = File(...),
            document_id: str = Form(...)
        ):
            """通过FormData上传文件"""
            try:
                logger.info(f"开始处理FormData文件上传: {file.filename}, document_id: {document_id}")

                # 验证文件类型
                if file.filename and not self._is_supported_file_type(file.filename):
                    raise HTTPException(
                        status_code=400,
                        detail=f"不支持的文件类型: {file.filename}"
                    )

                filename = file.filename or "unknown_file"

                # 检查文件是否已存在
                if config_manager.processing.skip_existing_files:
                    exists, existing_path = self.data_processor.check_file_exists(document_id, filename)
                    if exists:
                        logger.info(f"[UPLOAD_SKIP] 文件已存在，跳过处理 - document_id: {document_id}, file: {filename}, path: {existing_path}")
                        return ProcessResult(
                            success=True,
                            message="文件已存在，跳过处理",
                            data={"document_id": document_id, "status": "skipped", "existing_path": str(existing_path)}
                        )

                # 立即读取文件内容，避免在后台任务中文件被关闭
                logger.info(f"[FORMDATA_READ_START] 开始读取文件内容 - filename: {filename}, size: {file.size if hasattr(file, 'size') else 'unknown'}")
                start_time = time.time()
                file_content = await file.read()
                read_duration = time.time() - start_time
                logger.info(f"[FORMDATA_READ_COMPLETE] 文件读取完成 - duration: {read_duration:.2f}s, content_size: {len(file_content)} bytes")

                # 添加后台任务处理文件
                background_tasks.add_task(
                    self._process_file_pipeline,
                    "formdata",
                    document_id,
                    file_content=file_content,
                    filename=filename
                )
                
                return ProcessResult(
                    success=True,
                    message="文件上传任务已启动，正在后台处理",
                    data={"document_id": document_id, "filename": file.filename, "status": "processing"}
                )
                
            except HTTPException:
                raise  # 重新抛出HTTP异常，保持原始状态码
            except Exception as e:
                logger.error(f"FormData文件上传失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/status/{document_id}", response_model=ProcessResult)
        async def get_processing_status(document_id: str):
            """获取文档处理状态"""
            try:
                # 检查各个阶段的文件是否存在
                status_info = await self._check_processing_status(document_id)
                
                return ProcessResult(
                    success=True,
                    message="状态查询成功",
                    data=status_info
                )
                
            except Exception as e:
                logger.error(f"状态查询失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/tree/{document_id}", response_model=ProcessResult)
        async def get_document_tree(document_id: str):
            """获取文档树结构"""
            try:
                # 查找文档的元数据（这里简化处理，实际应该从数据库或缓存中获取）
                tree_info = await self._get_document_tree_info(document_id)
                
                return ProcessResult(
                    success=True,
                    message="文档树获取成功",
                    data=tree_info
                )
                
            except HTTPException:
                raise  # 重新抛出HTTP异常，保持原始状态码
            except Exception as e:
                logger.error(f"文档树获取失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/content/{document_id}/{file_path:path}")
        async def get_document_content(document_id: str, file_path: str):
            """获取文档块内容"""
            try:
                # 构建完整文件路径
                full_path = Path(config_manager.directory.base_document_tree_dir) / document_id / file_path
                
                if not full_path.exists() or not full_path.is_file():
                    raise HTTPException(status_code=404, detail="文档块不存在")
                
                content = await self.markdown_processor.read_document_chunk(str(full_path))
                
                return ProcessResult(
                    success=True,
                    message="文档内容获取成功",
                    data={"content": content, "file_path": file_path}
                )
                
            except HTTPException:
                raise  # 重新抛出HTTP异常，保持原始状态码
            except Exception as e:
                logger.error(f"文档内容获取失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/health")
        async def health_check():
            """健康检查"""
            return {"status": "healthy", "service": "LogLLM Data Access Service"}

        @self.app.get("/concurrent/status")
        async def get_concurrent_status():
            """获取并发处理状态"""
            return {
                "status": "success",
                "data": {
                    "processing_locks": len(self.processing_locks),
                    "active_documents": list(self.processing_locks)
                }
            }
    
    def _is_supported_file_type(self, filename: str) -> bool:
        """检查是否支持的文件类型"""
        if not filename:
            return False
        
        supported_extensions = {'.pdf', '.html', '.htm', '.md', '.markdown'}
        file_ext = Path(filename).suffix.lower()
        return file_ext in supported_extensions
    
    async def _process_file_pipeline(
        self,
        upload_type: str,
        document_id: str,
        **kwargs
    ) -> None:
        """文件处理流水线"""
        # 检查是否已经在处理中
        if document_id in self.processing_locks:
            logger.warning(f"[PIPELINE_SKIP] 文档已在处理中，跳过重复处理 - document_id: {document_id}")
            return

        # 注意：文件存在性检查已经在上传接口中完成，这里不再重复检查

        # 添加处理锁
        self.processing_locks.add(document_id)

        pipeline_start_time = time.time()
        try:
            logger.info(f"[PIPELINE_START] 开始处理文件流水线 - upload_type: {upload_type}, document_id: {document_id}")

            # 第一步：文件上传/下载/读取
            step_start_time = time.time()
            metadata = await self._handle_file_input(upload_type, document_id, **kwargs)
            step_duration = time.time() - step_start_time
            logger.info(f"[STEP_1_COMPLETE] 文件输入处理完成 - duration: {step_duration:.2f}s, metadata: {metadata}")

            # 第二步：PDF压缩（如果是PDF文件）
            step_start_time = time.time()
            metadata = await self.data_processor.compress_pdf(metadata)
            step_duration = time.time() - step_start_time
            logger.info(f"[STEP_2_COMPLETE] PDF压缩处理完成 - duration: {step_duration:.2f}s")

            # 第三步：文件格式转换
            step_start_time = time.time()
            markdown_path = await self.file_reformatter.convert_to_markdown(metadata)
            step_duration = time.time() - step_start_time
            logger.info(f"[STEP_3_COMPLETE] 文件格式转换完成 - duration: {step_duration:.2f}s, output: {markdown_path}")

            # 第四步：Markdown转文档树
            step_start_time = time.time()
            tree_path = await self.markdown_processor.process_markdown_to_tree(metadata)
            step_duration = time.time() - step_start_time
            logger.info(f"[STEP_4_COMPLETE] 文档树生成完成 - duration: {step_duration:.2f}s, output: {tree_path}")

            total_duration = time.time() - pipeline_start_time
            logger.info(f"[PIPELINE_SUCCESS] 文件处理流水线完成 - document_id: {document_id}, total_duration: {total_duration:.2f}s")

        except Exception as e:
            total_duration = time.time() - pipeline_start_time
            logger.error(f"[PIPELINE_ERROR] 文件处理流水线失败 - document_id: {document_id}, duration: {total_duration:.2f}s, error: {e}")
            import traceback
            logger.error(f"[PIPELINE_ERROR_DETAIL] 错误详情: {traceback.format_exc()}")
        finally:
            # 移除处理锁
            self.processing_locks.discard(document_id)
            logger.info(f"[PIPELINE_CLEANUP] 处理锁已释放 - document_id: {document_id}")



    async def _handle_file_input(
        self, 
        upload_type: str, 
        document_id: str, 
        **kwargs
    ) -> Dict[str, Any]:
        """处理文件输入"""
        if upload_type == "url":
            file_url = kwargs.get("file_url")
            return await self.data_processor.download_file(file_url, document_id)
        
        elif upload_type == "local":
            file_path = kwargs.get("file_path")
            return await self.data_processor.copy_file(file_path, document_id)
        
        elif upload_type == "formdata":
            file_content = kwargs.get("file_content")
            filename = kwargs.get("filename", "unknown_file")
            return await self.data_processor.write_file_tmp(file_content, filename, document_id)
        
        else:
            raise ValueError(f"不支持的上传类型: {upload_type}")
    
    async def _check_processing_status(self, document_id: str) -> Dict[str, Any]:
        """检查处理状态"""
        status = {
            "document_id": document_id,
            "stages": {
                "file_received": False,
                "markdown_converted": False,
                "document_tree_generated": False
            },
            "files": {}
        }
        
        # 检查接收文件目录
        receive_dir = Path(config_manager.directory.base_receive_dir) / document_id
        if receive_dir.exists():
            status["stages"]["file_received"] = True
            files = list(receive_dir.rglob("*"))  # 递归查找所有文件
            if files:
                status["files"]["received"] = [f.name for f in files if f.is_file()]

        # 检查markdown目录
        markdown_dir = Path(config_manager.directory.base_markdown_dir) / document_id
        if markdown_dir.exists():
            files = list(markdown_dir.rglob("*.md"))  # 递归查找所有markdown文件
            if files:
                status["stages"]["markdown_converted"] = True
                status["files"]["markdown"] = [f.name for f in files]

        # 检查文档树目录
        tree_dir = Path(config_manager.directory.base_document_tree_dir) / document_id
        if tree_dir.exists():
            status["stages"]["document_tree_generated"] = True
            status["files"]["tree_structure"] = "generated"
        
        return status
    
    async def _get_document_tree_info(self, document_id: str) -> Dict[str, Any]:
        """获取文档树信息"""
        tree_dir = Path(config_manager.directory.base_document_tree_dir) / document_id

        if not tree_dir.exists():
            raise HTTPException(status_code=404, detail="文档树不存在")

        # 简化的元数据（实际应该从数据库获取）
        metadata = {"document_id": document_id, "snow_id": "unknown"}

        try:
            return await self.markdown_processor.get_document_tree_info(metadata)
        except Exception as e:
            logger.error(f"获取文档树信息失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取文档树信息失败: {e}")
    
    def get_app(self) -> FastAPI:
        """获取FastAPI应用实例"""
        return self.app


# 创建服务实例
service = LogLLMDataAccessService()
app = service.get_app()

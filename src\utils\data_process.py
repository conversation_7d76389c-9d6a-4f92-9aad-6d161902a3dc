"""
数据处理工具模块
负责文件操作、雪花ID生成、文件上传下载等核心功能
"""

import os
import time
import shutil
import aiofiles
import requests
import pymupdf
import logging
from typing import Dict, Any, Optional, Tuple, TYPE_CHECKING
from pathlib import Path
from .config import config_manager

if TYPE_CHECKING:
    from fastapi import UploadFile

logger = logging.getLogger(__name__)

# 使用文件扩展名进行类型检测（不依赖magic库）


class SnowflakeIDGenerator:
    """雪花ID生成器"""
    
    def __init__(self, machine_id: int = 1, datacenter_id: int = 1):
        self.machine_id = machine_id
        self.datacenter_id = datacenter_id
        self.sequence = 0
        self.last_timestamp = -1
        
        # 各部分位数
        self.machine_id_bits = 5
        self.datacenter_id_bits = 5
        self.sequence_bits = 12
        
        # 最大值
        self.max_machine_id = -1 ^ (-1 << self.machine_id_bits)
        self.max_datacenter_id = -1 ^ (-1 << self.datacenter_id_bits)
        self.sequence_mask = -1 ^ (-1 << self.sequence_bits)
        
        # 位移
        self.machine_id_shift = self.sequence_bits
        self.datacenter_id_shift = self.sequence_bits + self.machine_id_bits
        self.timestamp_left_shift = self.sequence_bits + self.machine_id_bits + self.datacenter_id_bits
        
        # 起始时间戳 (2020-01-01)
        self.twepoch = 1577836800000
        
        if machine_id > self.max_machine_id or machine_id < 0:
            raise ValueError(f"机器ID必须在0到{self.max_machine_id}之间")
        if datacenter_id > self.max_datacenter_id or datacenter_id < 0:
            raise ValueError(f"数据中心ID必须在0到{self.max_datacenter_id}之间")
    
    def _current_millis(self) -> int:
        """获取当前毫秒时间戳"""
        return int(time.time() * 1000)
    
    def _til_next_millis(self, last_timestamp: int) -> int:
        """等待下一毫秒"""
        timestamp = self._current_millis()
        while timestamp <= last_timestamp:
            timestamp = self._current_millis()
        return timestamp
    
    def next_id(self) -> int:
        """生成下一个雪花ID"""
        timestamp = self._current_millis()
        
        if timestamp < self.last_timestamp:
            raise Exception("时钟回拨，拒绝生成ID")
        
        if self.last_timestamp == timestamp:
            self.sequence = (self.sequence + 1) & self.sequence_mask
            if self.sequence == 0:
                timestamp = self._til_next_millis(self.last_timestamp)
        else:
            self.sequence = 0
        
        self.last_timestamp = timestamp
        
        return ((timestamp - self.twepoch) << self.timestamp_left_shift) | \
               (self.datacenter_id << self.datacenter_id_shift) | \
               (self.machine_id << self.machine_id_shift) | \
               self.sequence


class DataProcess:
    """数据处理工具类"""

    def __init__(self):
        self.snowflake = SnowflakeIDGenerator()
        self.config = config_manager

        # 确保目录存在
        self.config._ensure_directories()
    

    
    async def write_file_tmp(self, file_content: bytes, raw_file_name: str, document_id: str) -> Dict[str, Any]:
        """文件tmp写入方法"""
        # 检查文件是否已存在
        if self.config.processing.skip_existing_files:
            existing_metadata = self.get_existing_file_metadata(document_id, raw_file_name)
            if existing_metadata:
                print(f"文件已存在，跳过处理: {raw_file_name}")
                return existing_metadata

        # 生成雪花ID和元数据
        snow_id = str(self.snowflake.next_id())+"_"+str(raw_file_name).rsplit('.',1)[0]
        file_ext = Path(raw_file_name).suffix
        tmp_file_name = f"{snow_id}{file_ext}"

        # 使用新的目录结构：document_id/snow_id/文件
        final_path = self.config.get_file_path(document_id, snow_id, tmp_file_name, "receive")
        final_path.parent.mkdir(parents=True, exist_ok=True)

        # 写入文件
        async with aiofiles.open(final_path, 'wb') as f:
            await f.write(file_content)

        # 创建文件元数据
        metadata = {
            "raw_file_name": raw_file_name,
            "file_type": self.get_file_type(str(final_path)),
            "snow_id": snow_id,
            "tmp_file_name": tmp_file_name,
            "timestamp": str(int(time.time())),
            "document_id": document_id,
            "file_path": str(final_path)
        }

        return metadata
    
    async def write_file_normal(self, file_path: str, content: bytes) -> bool:
        """文件普通写入方法"""
        try:
            file_path_obj = Path(file_path)
            file_path_obj.parent.mkdir(parents=True, exist_ok=True)
            
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(content)
            return True
        except Exception as e:
            print(f"文件写入失败: {e}")
            return False
    
    async def copy_file(self, src_path: str, document_id: str) -> Dict[str, Any]:
        """文件复制方法"""
        src_path_obj = Path(src_path)
        if not src_path_obj.exists():
            raise FileNotFoundError(f"源文件不存在: {src_path}")
        
        # 读取文件内容
        async with aiofiles.open(src_path, 'rb') as f:
            content = await f.read()
        
        # 调用tmp写入方法
        return await self.write_file_tmp(content, src_path_obj.name, document_id)
    
    async def read_file(self, file_path: str) -> bytes:
        """文件读取方法"""
        async with aiofiles.open(file_path, 'rb') as f:
            return await f.read()
    
    async def download_file(self, file_url: str, document_id: str) -> Dict[str, Any]:
        """文件下载方法"""
        try:
            response = requests.get(file_url, stream=True)
            response.raise_for_status()
            
            # 从URL获取文件名
            file_name = Path(file_url).name
            if not file_name or '.' not in file_name:
                # 如果无法从URL获取文件名，使用Content-Disposition
                content_disposition = response.headers.get('content-disposition')
                if content_disposition:
                    import re
                    filename_match = re.search(r'filename="?([^"]+)"?', content_disposition)
                    if filename_match:
                        file_name = filename_match.group(1)
                    else:
                        file_name = f"download_{int(time.time())}.bin"
                else:
                    file_name = f"download_{int(time.time())}.bin"
            
            # 下载文件内容
            content = response.content
            
            # 调用tmp写入方法
            return await self.write_file_tmp(content, file_name, document_id)
            
        except Exception as e:
            raise Exception(f"文件下载失败: {e}")
    
    async def parse_formdata_file(self, upload_file: "UploadFile", document_id: str) -> Dict[str, Any]:
        """解析formdata文件方法"""
        try:
            # 确保文件名存在
            filename = upload_file.filename or "unknown_file"

            # 读取上传文件内容
            content = await upload_file.read()

            # 重置文件指针（如果需要的话）
            if hasattr(upload_file, 'seek'):
                try:
                    await upload_file.seek(0)
                except:
                    pass  # 忽略seek错误

            # 调用tmp写入方法
            return await self.write_file_tmp(content, filename, document_id)

        except Exception as e:
            raise Exception(f"FormData文件解析失败: {e}")
    
    async def copy_to_markdown_dir(self, metadata: Dict[str, Any], content: bytes) -> str:
        """将文件复制到markdown目录"""
        markdown_filename = f"{metadata['snow_id']}_markdown.md"
        markdown_path = self.config.get_file_path(
            metadata["document_id"],
            metadata["snow_id"],
            markdown_filename,
            "markdown"
        )
        markdown_path.parent.mkdir(parents=True, exist_ok=True)

        async with aiofiles.open(markdown_path, 'wb') as f:
            await f.write(content)

        return str(markdown_path)

    def check_file_exists(self, document_id: str, original_filename: str) -> tuple[bool, Optional[str]]:
        """
        检查文件是否已存在于知识库中

        Args:
            document_id: 文档ID（知识库ID）
            original_filename: 原始文件名

        Returns:
            (是否存在, 已存在文件的路径)
        """
        if not self.config.processing.check_existing_files:
            return False, None

        # 检查接收目录中是否已有同名文件
        receive_dir = Path(self.config.directory.base_receive_dir) / document_id

        if not receive_dir.exists():
            return False, None

        # 遍历所有子目录查找同名文件
        for item in receive_dir.rglob("*"):
            if item.is_file():
                # 检查是否是同一个原始文件名
                if item.name == original_filename:
                    return True, str(item)

                # 检查是否是重命名后的文件（包含原始文件名）
                if original_filename in item.name and item.name.endswith(Path(original_filename).suffix):
                    return True, str(item)

        return False, None

    def get_existing_file_metadata(self, document_id: str, original_filename: str) -> Optional[Dict[str, Any]]:
        """
        获取已存在文件的元数据

        Args:
            document_id: 文档ID
            original_filename: 原始文件名

        Returns:
            文件元数据字典，如果文件不存在则返回None
        """
        exists, file_path = self.check_file_exists(document_id, original_filename)

        if not exists:
            return None

        file_path_obj = Path(file_path)

        # 尝试从文件路径推断snow_id
        snow_id = None
        if self.config.directory.use_snow_id_subdirs:
            # 从路径中提取snow_id：document_id/snow_id/filename
            parts = file_path_obj.parts
            if len(parts) >= 3:
                snow_id = parts[-2]  # 倒数第二个部分应该是snow_id

        if not snow_id:
            # 如果无法从路径提取，尝试从文件名提取
            stem = file_path_obj.stem
            if '_' in stem:
                potential_snow_id = stem.split('_')[0]
                if potential_snow_id.isdigit():
                    snow_id = potential_snow_id

        return {
            "raw_file_name": original_filename,
            "file_type": self.get_file_type(file_path),
            "snow_id": snow_id or "unknown",
            "tmp_file_name": file_path_obj.name,
            "timestamp": str(int(file_path_obj.stat().st_mtime)),
            "document_id": document_id,
            "file_path": file_path
        }

    def get_file_type(self, file_path: str) -> str:
        """根据文件扩展名确定文件类型"""
        ext = Path(file_path).suffix.lower()

        if ext in self.config.processing.supported_pdf_extensions:
            return "pdf"
        elif ext in self.config.processing.supported_html_extensions:
            return "html"
        elif ext in self.config.processing.supported_markdown_extensions:
            return "markdown"
        else:
            return "unknown"

    async def compress_pdf(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        压缩PDF文件

        Args:
            metadata: 文件元数据字典

        Returns:
            压缩后的文件元数据
        """
        # 只处理PDF文件
        if metadata.get("file_type", "").lower() != "pdf":
            logger.info(f"[PDF_COMPRESS_SKIP] 非PDF文件，跳过压缩 - file_type: {metadata.get('file_type')}")
            return metadata

        try:
            # 获取源文件路径
            src_file = Path(metadata.get("file_path"))
            if not src_file.exists():
                logger.error(f"[PDF_COMPRESS_ERROR] 源文件不存在: {src_file}")
                return metadata

            logger.info(f"[PDF_COMPRESS_START] 开始压缩PDF文件: {src_file}")
            start_time = time.time()

            # 创建压缩后的文件路径（在同一目录下）
            compressed_filename = f"{metadata['snow_id']}_compressed{src_file.suffix}"
            compressed_path = src_file.parent / compressed_filename

            # 使用pymupdf压缩PDF
            doc = pymupdf.open(str(src_file))

            # 执行压缩，garbage=4表示最高压缩级别
            doc.save(str(compressed_path), garbage=4, deflate=True, clean=True)
            doc.close()

            # 检查压缩效果
            original_size = src_file.stat().st_size
            compressed_size = compressed_path.stat().st_size
            compression_ratio = (1 - compressed_size / original_size) * 100 if original_size > 0 else 0

            duration = time.time() - start_time
            logger.info(f"[PDF_COMPRESS_SUCCESS] PDF压缩完成 - duration: {duration:.2f}s, "
                       f"original: {original_size} bytes, compressed: {compressed_size} bytes, "
                       f"ratio: {compression_ratio:.1f}%")

            # 如果压缩后文件更小，替换原文件
            if compressed_size < original_size:
                # 删除原文件
                src_file.unlink()
                # 重命名压缩文件为原文件名
                compressed_path.rename(src_file)
                logger.info(f"[PDF_COMPRESS_REPLACE] 使用压缩后的文件替换原文件")
            else:
                # 如果压缩后文件更大，删除压缩文件，保留原文件
                compressed_path.unlink()
                logger.info(f"[PDF_COMPRESS_KEEP_ORIGINAL] 压缩后文件更大，保留原文件")

            return metadata

        except Exception as e:
            logger.error(f"[PDF_COMPRESS_ERROR] PDF压缩失败: {e}")
            # 压缩失败时返回原始元数据，不影响后续流程
            return metadata
